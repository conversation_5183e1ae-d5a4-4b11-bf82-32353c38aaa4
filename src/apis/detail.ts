import http from "../utils/http";
import  { type ResType } from "../apis/ResTypes";
export type DetailType = {
  art_id: string;
  title: string;
  aut_id: string;
  pubdate: string;
  aut_name: string;
  is_top: number;
  content:string;
  aut_photo:string;
  is_followed:boolean;
  is_collected:boolean;
  attitude:boolean;
  comm_count:string;
  read_count:string;
  like_count:string;

}
export function getDetailApi(id:string) {
  return http.request<ResType<DetailType>>({
    url:`/articles/${id}`,
    method:'get'
  })
}