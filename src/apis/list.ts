import http from '../utils/http';
import type { ResType } from './ResTypes';

export type channelsItem = {
  id: number;
  name: string;
}

export type channelsRes = {
  channels: channelsItem[];
}
/**
 * 获取频道列表的API请求函数
 *
 * @returns 返回频道列表的HTTP请求结果
 */
export function getChannelsApi() {
   return http.request<ResType<channelsRes>>({
        url: '/channels',
        method: 'get',
    })
}

 type listItem = {
  art_id: string;
  title: string;
  aut_id: string;
  comm_count: number;
  pubdate: string;
  aut_name: string;
  is_top: number;
  cover:{
    type:string;
    images:string[];
  }
}

export type listRes = {
  results:listItem[]
  pre_timestamp:string
}
type ResParams = {
  channel_id:string;
  timestamp:string;
}
/**
 * 获取文章列表的API函数
 *
 * @param params 请求参数，包含分页信息等
 * @returns 返回获取到的文章列表数据
 */
export function  getListApi(params:ResParams){
   return http.request<ResType<listRes>>({
        url: '/articles',
        method: 'get',
        params
    })
}

