import React, { useEffect,useState } from 'react'
// import { useParams } from 'react-router-dom'
import { type DetailType } from '../../apis/detail'
import { getDetailApi } from '../../apis/detail'
import { useLocation } from 'react-router-dom';
function Detail() {
  const [detailList, setDetailList] =useState<DetailType|null>(null);

 const location = useLocation();
  const { state } = location;
  console.log(state);

  useEffect(() => {
    const getDetailList=async ()=>{
    // console.log(useParams());
      const res=await getDetailApi(state.id!)
      // const{ data} = res.data.data
      console.log(res.data.data);
      setDetailList(res.data.data);
  }
  getDetailList();
  }, [state.id])
  return (
    <div>{
        detailList?.content
      }</div>
  )
}

export default Detail