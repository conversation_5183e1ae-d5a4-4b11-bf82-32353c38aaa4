import  { useEffect, useState } from 'react'
import { getChannelsApi } from '../../apis/list'
import type { channelsItem } from '../../apis/list'

function useTabs(){

  const [channel, setChannels] = useState<channelsItem[]>([])
  useEffect(() => {
    const getChannels = async () => {
      try {
        const res = await getChannelsApi()
        const { channels } = res.data.data
        setChannels(channels)
        console.log('频道列表:', channels)
      } catch (error) {
        console.log(error)
      }
    }
    getChannels()
  }, [])
  return { channel }
}
export { useTabs }
