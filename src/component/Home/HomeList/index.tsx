import { useEffect, useState } from 'react'
import { Image, List, InfiniteScroll } from 'antd-mobile'
// import { users } from './users'
import { getListApi } from '../../../apis/list'
import type { listRes } from '../../../apis/list'
import { useNavigate } from 'react-router-dom'

type ResParams = {
  channelId: string
}
function HomeList(prpos: ResParams) {
  const { channelId } = prpos
  const [list, setList] = useState<listRes>({
    results: [],
    pre_timestamp: '' + new Date().getTime(),
  })
  useEffect(() => {
    const getList = async () => {
      const res = await getListApi({
        channel_id: channelId,
        timestamp: '' + new Date().getTime(),
      })
      const { results, pre_timestamp } = res.data.data
      setList({ results, pre_timestamp })
      console.log(results)
    }
    getList()
  }, [channelId])

  const [hasMore, setHasMore] = useState<boolean>(true)
  const loadMore = async () => {
    try {
      const res = await getListApi({
        channel_id: channelId,
        timestamp: list.pre_timestamp,
      })
      const { results, pre_timestamp } = res.data.data
      setList({
        results: [...list.results, ...results],
        pre_timestamp,
      })
      if (results.length === 0) {
        setHasMore(false)
      }
      console.log(results)
    } catch (error) {
      console.log(error)
    }
  }
  const navGet = useNavigate()
  const Gotodetail = (id: string) => {
    navGet('/detail', {
      state: {
        id,
      },
    })
  }
  return (
    <div>
      <List>
        {list.results.map((user) => (
          <List.Item
            onClick={() => Gotodetail(user.art_id)}
            key={user.art_id}
            title={user.pubdate}
            prefix={
              <Image
                src={user.cover.images?.[0]}
                style={{ borderRadius: 20 }}
                fit="cover"
                width={40}
                height={40}
              />
            }
            description={user.aut_name}
          >
            {user.title}
          </List.Item>
        ))}
      </List>
      <InfiniteScroll loadMore={loadMore} hasMore={hasMore} threshold={10} />
    </div>
  )
}

export default HomeList
