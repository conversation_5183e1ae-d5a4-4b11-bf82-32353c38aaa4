
import { Tabs } from 'antd-mobile'
import { useTabs } from './useTabs'
import HomeList from './HomeList'
function Home() {
  const {channel} = useTabs()
  return (
    <>
      <div>

          <Tabs>
             {channel.map((item=>
               <Tabs.Tab title={item.name} key={item.id}>
               <div>
                 <HomeList channelId={"" + item.id} />
               </div>
               </Tabs.Tab>)
            )}
          </Tabs>

      </div>
    </>
  )
}

export default Home
